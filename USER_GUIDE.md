# دليل المستخدم - نظام إدارة مؤسسة وقود المستقبل

## 🚀 البدء السريع

### تشغيل التطبيق
1. افتح التطبيق من سطح المكتب
2. ستظهر لوحة التحكم الرئيسية
3. استخدم شريط التنقل العلوي للانتقال بين الأقسام

### تغيير اللغة
1. اذهب إلى قسم "الإعدادات" ⚙️
2. اختر اللغة المطلوبة من القائمة المنسدلة
3. اضغط "حفظ الإعدادات"

## 📋 الأقسام الرئيسية

### 🏠 لوحة التحكم
**الغرض:** عرض ملخص سريع لحالة المؤسسة

**المميزات:**
- إحصائيات العملاء والموردين
- عدد البطاقات النشطة
- مواعيد اليوم
- تنبيهات البطاقات المنتهية

**كيفية الاستخدام:**
- اضغط على أي بطاقة للانتقال إلى القسم المرتبط بها

### 🆔 بطاقات الغاز
**الغرض:** إدارة بطاقات الغاز للعملاء

**إضافة بطاقة جديدة:**
1. اضغط "إضافة بطاقة جديدة"
2. املأ البيانات المطلوبة:
   - رقم البطاقة (فريد)
   - اسم العميل
   - نوع الغاز
   - الرصيد الأولي
   - تاريخ الانتهاء
3. اضغط "إضافة"

**البحث والتصفية:**
- استخدم مربع البحث للبحث بالاسم أو رقم البطاقة
- استخدم قائمة التصفية لعرض بطاقات معينة (نشطة/منتهية/تنتهي قريباً)

**الإجراءات المتاحة:**
- ✏️ تعديل البطاقة
- 🗑️ حذف البطاقة

### 👥 الزبائن
**الغرض:** إدارة قاعدة بيانات العملاء

**إضافة عميل جديد:**
1. اضغط "إضافة عميل جديد"
2. املأ البيانات:
   - الاسم الكامل
   - رقم الهاتف
   - المدينة
   - نوع الخدمة
   - العنوان (اختياري)
3. اضغط "إضافة"

**طباعة شهادة العضوية:**
1. اضغط على أيقونة الطباعة 🖨️ بجانب اسم العميل
2. ستفتح نافذة جديدة مع الشهادة
3. اضغط Ctrl+P للطباعة

**تصدير قائمة العملاء:**
- اضغط "تصدير قائمة العملاء" لحفظ البيانات كملف CSV

### 📅 المواعيد
**الغرض:** جدولة وإدارة المواعيد

**إضافة موعد جديد:**
1. اضغط "إضافة موعد جديد"
2. اختر العميل من القائمة
3. أدخل رقم السيارة
4. اختر نوع الخدمة
5. حدد التاريخ والوقت
6. أضف ملاحظات إضافية (اختياري)

**تصفية المواعيد:**
- استخدم مرشح التاريخ لعرض مواعيد يوم معين
- استخدم مرشح نوع الخدمة

### 🚚 الموردين
**الغرض:** إدارة قاعدة بيانات الموردين

**إضافة مورد جديد:**
1. اضغط "إضافة مورد جديد"
2. املأ بيانات المورد:
   - اسم المورد
   - رقم الهاتف
   - المدينة
   - نوع المنتج
   - العنوان
   - الحالة (نشط/غير نشط)

**مراقبة الموردين:**
- عرض عدد الموردين النشطين
- إجمالي الموردين المسجلين

### 📦 المخزون
**الغرض:** تتبع المنتجات والكميات

**إضافة منتج جديد:**
1. اضغط "إضافة منتج"
2. أدخل بيانات المنتج:
   - اسم المنتج
   - الكمية الحالية
   - الحد الأدنى للتنبيه
   - الوحدة (قطعة، كيلو، لتر، إلخ)

**تنبيهات المخزون:**
- المنتجات التي تصل للحد الأدنى تظهر بلون تحذيري
- عرض عدد المنتجات منخفضة المخزون

**تقرير المخزون:**
- اضغط "تقرير المخزون" لطباعة تقرير شامل

### 🛒 المبيعات
**الغرض:** تسجيل وتتبع عمليات البيع

**إضافة عملية بيع:**
1. اضغط "عملية بيع جديدة"
2. اختر العميل
3. حدد نوع الخدمة
4. أدخل المبلغ
5. سيتم توليد رقم فاتورة تلقائياً

**مراقبة المبيعات:**
- عرض مبيعات اليوم
- عدد العمليات اليومية
- طباعة الفواتير

### 💰 الديون
**الغرض:** إدارة ديون العملاء

**إضافة دين جديد:**
1. اضغط "إضافة دين جديد"
2. اختر العميل
3. أدخل المبلغ
4. حدد تاريخ الاستحقاق
5. أضف وصف للدين

**تسديد الديون:**
- اضغط "تسديد" بجانب الدين المراد تسديده
- سيتم تحديث حالة الدين إلى "مدفوع"

**تنبيهات الديون:**
- الديون المتأخرة تظهر بلون أحمر
- عرض إجمالي الديون المستحقة

## 🔧 الإعدادات

### الإعدادات العامة
- **اسم المؤسسة:** يظهر في التقارير والفواتير
- **عنوان المؤسسة:** يظهر في الشهادات
- **رقم الهاتف:** للتواصل

### إعدادات النظام
- **اللغة:** العربية أو الفرنسية
- **العملة:** ريال سعودي، دولار، يورو

## 📊 التقارير

### أنواع التقارير المتاحة:
1. **تقرير المخزون:** قائمة شاملة بالمنتجات والكميات
2. **تقرير المبيعات:** ملخص المبيعات الشهرية
3. **تقرير الديون:** حالة الديون والمستحقات
4. **فواتير المبيعات:** فواتير فردية قابلة للطباعة

### طباعة التقارير:
1. اضغط على زر التقرير المطلوب
2. ستفتح نافذة جديدة مع التقرير
3. استخدم Ctrl+P للطباعة

## 💡 نصائح مهمة

### النسخ الاحتياطي:
- يتم حفظ البيانات تلقائياً في مجلد `data`
- انسخ هذا المجلد بانتظام كنسخة احتياطية

### الأداء:
- أغلق النوافذ غير المستخدمة
- استخدم البحث والتصفية لتسريع العثور على البيانات

### الأمان:
- لا تشارك ملفات البيانات مع أشخاص غير مخولين
- استخدم كلمات مرور قوية إذا كان النظام يدعم ذلك

## ❓ حل المشاكل الشائعة

### التطبيق لا يبدأ:
1. تأكد من تثبيت Node.js
2. شغل `npm install` في مجلد التطبيق
3. جرب `npm start`

### البيانات لا تظهر:
1. تحقق من وجود ملف `data/app-data.json`
2. أعد تشغيل التطبيق
3. إذا استمرت المشكلة، احذف الملف وسيتم إنشاء بيانات تجريبية

### مشاكل الطباعة:
1. تأكد من تثبيت طابعة افتراضية
2. جرب طباعة من متصفح آخر
3. تحقق من إعدادات الطابعة

---

للمزيد من المساعدة، راجع ملف README.md أو تواصل مع الدعم الفني.
