# نظام إدارة مؤسسة وقود المستقبل
## Future Fuel Management System

تطبيق سطح مكتب شامل لإدارة مؤسسات الوقود والغاز، مبني بتقنية Electron مع دعم اللغتين العربية والفرنسية.

## 🌟 المميزات الرئيسية

### 🔁 دعم ثنائي اللغة
- العربية (RTL)
- الفرنسية (LTR)
- إمكانية التبديل بين اللغتين من الإعدادات

### 💻 التوافق
- Windows 7, 8, 10, 11
- تصميم خفيف ومتجاوب
- لا يتطلب اتصال بالإنترنت

### 🧩 الوحدات المتاحة

#### 🏠 لوحة التحكم
- إحصائيات شاملة للعمليات
- بطاقات ملونة تفاعلية
- تنبيهات للمهام المستعجلة

#### 🆔 بطاقات الغاز
- إدارة بطاقات العملاء
- تتبع تواريخ الانتهاء
- تنبيهات للبطاقات المنتهية

#### 👥 الزبائن
- قاعدة بيانات شاملة للعملاء
- طباعة شهادات العضوية
- تصنيف حسب نوع الخدمة

#### 📅 المواعيد
- جدولة المواعيد
- تصفية حسب التاريخ والنوع
- تذكيرات تلقائية

#### 🚚 الموردين
- إدارة قاعدة بيانات الموردين
- تتبع حالة الموردين
- إحصائيات الموردين النشطين

#### 📦 المخزون
- تتبع المنتجات والكميات
- تنبيهات المخزون المنخفض
- تقارير المخزون التفصيلية

#### 🛒 المبيعات
- تسجيل عمليات البيع
- فواتير قابلة للطباعة
- تقارير المبيعات اليومية والشهرية

#### 🛍️ المشتريات
- تسجيل عمليات الشراء
- ربط بقاعدة بيانات الموردين
- تتبع المصروفات

#### 💰 الديون
- إدارة ديون العملاء
- تنبيهات الديون المستحقة
- تتبع حالة السداد

#### 📊 جدول الإرسال
- تسجيل عمليات التوصيل
- تتبع السائقين والوجهات
- تقارير الإرسال

#### ⚙️ الإعدادات
- إعدادات المؤسسة
- تخصيص اللغة والعملة
- إدارة المستخدمين (اختياري)

## 🚀 التشغيل

### متطلبات النظام
- Node.js 16 أو أحدث
- npm أو yarn

### التثبيت والتشغيل
```bash
# تثبيت المتطلبات
npm install

# تشغيل التطبيق في وضع التطوير
npm start

# بناء التطبيق للتوزيع
npm run build
```

## 📁 هيكل المشروع

```
fuel-management-app/
├── assets/                 # الأصول والأيقونات
├── data/                   # ملفات البيانات
│   └── sample-data.json    # بيانات تجريبية
├── lang/                   # ملفات اللغة
│   ├── ar.json            # النصوص العربية
│   └── fr.json            # النصوص الفرنسية
├── pages/                  # صفحات HTML إضافية
├── scripts/                # ملفات JavaScript
│   ├── app.js             # التطبيق الرئيسي
│   ├── data-manager.js    # إدارة البيانات
│   ├── forms.js           # إدارة النماذج
│   └── reports.js         # إدارة التقارير
├── styles/                 # ملفات CSS إضافية
├── index.html             # الصفحة الرئيسية
├── main.js                # ملف Electron الرئيسي
├── preload.js             # ملف preload
├── style.css              # الأنماط الرئيسية
└── package.json           # إعدادات المشروع
```

## 🔧 الميزات التقنية

### الأمان
- Context Isolation مفعل
- Node Integration معطل
- Web Security مفعل
- Preload script آمن

### الأداء
- تحميل البيانات بشكل تدريجي
- تخزين محلي للبيانات
- واجهة مستخدم متجاوبة
- تحديث تلقائي للإحصائيات

### التخزين
- ملفات JSON محلية
- نسخ احتياطية تلقائية
- تصدير البيانات بصيغة CSV
- استيراد البيانات

## 📊 التقارير المتاحة

- تقرير المخزون التفصيلي
- تقرير المبيعات الشهري
- تقرير الديون والمستحقات
- فواتير المبيعات والمشتريات
- شهادات العضوية للعملاء

## 🎨 التصميم

- تصميم عصري بألوان متدرجة
- واجهة سهلة الاستخدام
- دعم RTL و LTR
- أيقونات تعبيرية واضحة
- تجربة مستخدم متسقة

## 🔄 التحديثات المستقبلية

- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تكامل مع الطابعات الحرارية
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام إدارة المستخدمين المتقدم
- [ ] تطبيق الهاتف المحمول المرافق
- [ ] تكامل مع أنظمة المحاسبة

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**مؤسسة وقود المستقبل** - نحو مستقبل أفضل في إدارة الوقود والغاز
