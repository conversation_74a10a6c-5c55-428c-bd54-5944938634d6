/* إعدادات عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  direction: rtl;
  text-align: right;
}

body.ltr {
  direction: ltr;
  text-align: left;
}

/* رأس الصفحة */
header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

header h1 {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* شريط التنقل */
nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

nav button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  min-width: 120px;
}

nav button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

nav button.active {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 6px 20px rgba(118, 75, 162, 0.4);
}

/* المحتوى الرئيسي */
main {
  padding: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.page {
  display: none;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page.active {
  display: block;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.page h2 {
  color: #667eea;
  font-size: 32px;
  margin-bottom: 30px;
  font-weight: 600;
  text-align: center;
  position: relative;
}

.page h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* بطاقات لوحة التحكم */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.card h3 {
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 600;
}

.card p {
  opacity: 0.9;
  margin-bottom: 15px;
  font-size: 14px;
}

.card-stats {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-top: 15px;
}

/* الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2px solid #667eea;
  padding: 10px 23px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.page-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

/* الجداول */
.table-container {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

table th {
  padding: 15px 12px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
}

table td {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

table tbody tr:hover {
  background-color: #f8f9ff;
}

table tbody tr:nth-child(even) {
  background-color: #fafbff;
}

/* حالات البطاقات والديون */
.status-active {
  background: #10b981;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-expired {
  background: #ef4444;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-expiring {
  background: #f59e0b;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-overdue {
  background: #ef4444;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-paid {
  background: #10b981;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

/* أزرار الإجراءات */
.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.action-btn.edit {
  color: #3b82f6;
}

.action-btn.edit:hover {
  background: rgba(59, 130, 246, 0.1);
}

.action-btn.delete {
  color: #ef4444;
}

.action-btn.delete:hover {
  background: rgba(239, 68, 68, 0.1);
}

.action-btn.print {
  color: #10b981;
}

.action-btn.print:hover {
  background: rgba(16, 185, 129, 0.1);
}

/* النماذج */
.form-container {
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

/* المودال */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.modal-header h3 {
  color: #667eea;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #9ca3af;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* صفحة الإعدادات */
.settings-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.settings-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
  color: #667eea;
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.setting-item input,
.setting-item select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.setting-item input:focus,
.setting-item select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* إحصائيات سريعة */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #667eea;
}

.stat-card h4 {
  color: #667eea;
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.stat-card .stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

/* تنبيهات */
.alert {
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-left: 4px solid #10b981;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
  border-left: 4px solid #f59e0b;
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
  border-left: 4px solid #ef4444;
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-left: 4px solid #3b82f6;
}

/* شريط البحث */
.search-container {
  margin-bottom: 20px;
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.search-input {
  padding: 12px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 14px;
  min-width: 300px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-select {
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  main {
    padding: 15px;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
  }

  nav {
    padding: 10px;
  }

  nav button {
    min-width: 100px;
    padding: 10px 15px;
    font-size: 12px;
  }

  .page {
    padding: 20px;
  }

  .page h2 {
    font-size: 24px;
  }

  .table-container {
    overflow-x: auto;
  }

  table {
    min-width: 600px;
  }

  .settings-container {
    grid-template-columns: 1fr;
  }

  .search-input {
    min-width: 250px;
  }

  .form-container {
    padding: 20px;
  }

  .modal-content {
    padding: 20px;
    width: 95%;
  }
}

@media (max-width: 480px) {
  header h1 {
    font-size: 20px;
  }

  nav button {
    min-width: 80px;
    padding: 8px 12px;
    font-size: 11px;
  }

  .card {
    padding: 20px;
  }

  .card h3 {
    font-size: 16px;
  }

  .card-stats {
    font-size: 20px;
  }

  .search-input {
    min-width: 200px;
  }
}

/* طباعة الشهادة */
.certificate {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  color: #333;
}

.certificate-header {
  border-bottom: 3px solid #667eea;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.certificate-title {
  font-size: 32px;
  color: #667eea;
  font-weight: 700;
  margin-bottom: 10px;
}

.certificate-subtitle {
  font-size: 18px;
  color: #666;
  font-weight: 500;
}

.certificate-body {
  padding: 30px 0;
  line-height: 1.8;
  font-size: 16px;
}

.certificate-footer {
  border-top: 2px solid #f0f0f0;
  padding-top: 20px;
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* عناصر المواعيد */
.appointment-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #667eea;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.appointment-header h4 {
  color: #667eea;
  font-size: 18px;
  margin: 0;
}

.appointment-time {
  background: #667eea;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.appointment-details p {
  margin: 5px 0;
  color: #666;
}

.appointment-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

/* عناصر المخزون */
.inventory-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
}

.inventory-item.low-stock {
  border-left-color: #f59e0b;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-header h4 {
  color: #333;
  font-size: 18px;
  margin: 0;
}

.status-warning {
  background: #f59e0b;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

/* عناصر المبيعات والمشتريات */
.sale-item, .purchase-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
}

.purchase-item {
  border-left-color: #3b82f6;
}

.sale-header, .purchase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.sale-amount, .purchase-amount {
  background: #10b981;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
}

.purchase-amount {
  background: #3b82f6;
}

/* عناصر الديون */
.debt-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f59e0b;
}

.debt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.debt-amount {
  background: #f59e0b;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
}

/* عناصر الإرسال */
.dispatch-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #8b5cf6;
}

.dispatch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.dispatch-date {
  background: #8b5cf6;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

@media print {
  body {
    background: white;
  }

  nav, .page-actions {
    display: none;
  }

  .certificate {
    box-shadow: none;
    border: 2px solid #667eea;
  }
}