// إدارة التقويم والمواعيد
class CalendarManager {
  constructor(app) {
    this.app = app;
    this.currentDate = new Date();
    this.selectedDate = null;
  }

  // عرض التقويم
  showCalendarView() {
    const calendarHTML = this.generateCalendarHTML();
    
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.id = 'calendar-modal';
    modal.innerHTML = `
      <div class="modal-content calendar-modal-content">
        <div class="modal-header">
          <h3>تقويم المواعيد</h3>
          <button class="close-btn" onclick="hideCalendarModal()">&times;</button>
        </div>
        <div class="modal-body">
          ${calendarHTML}
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    this.loadCalendarAppointments();
  }

  // توليد HTML التقويم
  generateCalendarHTML() {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let calendarHTML = `
      <div class="calendar-container">
        <div class="calendar-header">
          <button class="calendar-nav-btn" onclick="calendarManager.previousMonth()">&lt;</button>
          <h3>${monthNames[month]} ${year}</h3>
          <button class="calendar-nav-btn" onclick="calendarManager.nextMonth()">&gt;</button>
        </div>
        <div class="calendar-grid">
          <div class="calendar-weekdays">
            <div class="weekday">الأحد</div>
            <div class="weekday">الاثنين</div>
            <div class="weekday">الثلاثاء</div>
            <div class="weekday">الأربعاء</div>
            <div class="weekday">الخميس</div>
            <div class="weekday">الجمعة</div>
            <div class="weekday">السبت</div>
          </div>
          <div class="calendar-days">
    `;

    // إضافة الأيام الفارغة في بداية الشهر
    for (let i = 0; i < startingDayOfWeek; i++) {
      calendarHTML += '<div class="calendar-day empty"></div>';
    }

    // إضافة أيام الشهر
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateString = date.toISOString().split('T')[0];
      const isToday = this.isToday(date);
      const hasAppointments = this.hasAppointmentsOnDate(dateString);
      
      calendarHTML += `
        <div class="calendar-day ${isToday ? 'today' : ''} ${hasAppointments ? 'has-appointments' : ''}" 
             onclick="calendarManager.selectDate('${dateString}')">
          <span class="day-number">${day}</span>
          ${hasAppointments ? '<div class="appointment-indicator"></div>' : ''}
        </div>
      `;
    }

    calendarHTML += `
          </div>
        </div>
        <div class="calendar-appointments" id="calendar-appointments">
          <h4>مواعيد اليوم المحدد</h4>
          <div id="selected-date-appointments">اختر يوماً لعرض المواعيد</div>
        </div>
      </div>
    `;

    return calendarHTML;
  }

  // التحقق من وجود مواعيد في تاريخ معين
  hasAppointmentsOnDate(dateString) {
    return this.app.data.appointments.some(appointment => 
      appointment.date === dateString
    );
  }

  // التحقق من كون التاريخ هو اليوم
  isToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  // الانتقال للشهر السابق
  previousMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.updateCalendar();
  }

  // الانتقال للشهر التالي
  nextMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    this.updateCalendar();
  }

  // تحديث التقويم
  updateCalendar() {
    const modalBody = document.querySelector('#calendar-modal .modal-body');
    if (modalBody) {
      modalBody.innerHTML = this.generateCalendarHTML();
      this.loadCalendarAppointments();
    }
  }

  // اختيار تاريخ
  selectDate(dateString) {
    this.selectedDate = dateString;
    
    // إزالة التحديد السابق
    document.querySelectorAll('.calendar-day').forEach(day => {
      day.classList.remove('selected');
    });

    // إضافة التحديد الجديد
    event.target.closest('.calendar-day').classList.add('selected');

    // عرض مواعيد اليوم المحدد
    this.showAppointmentsForDate(dateString);
  }

  // عرض مواعيد تاريخ معين
  showAppointmentsForDate(dateString) {
    const appointments = this.app.data.appointments.filter(appointment => 
      appointment.date === dateString
    );

    const container = document.getElementById('selected-date-appointments');
    if (!container) return;

    if (appointments.length === 0) {
      container.innerHTML = `<p>لا توجد مواعيد في ${formatDate(dateString)}</p>`;
      return;
    }

    let appointmentsHTML = `<h5>مواعيد ${formatDate(dateString)} (${appointments.length})</h5>`;
    
    appointments.forEach(appointment => {
      appointmentsHTML += `
        <div class="calendar-appointment-item">
          <div class="appointment-time">${appointment.time}</div>
          <div class="appointment-details">
            <strong>${appointment.customerName}</strong><br>
            ${appointment.serviceType}<br>
            <small>رقم السيارة: ${appointment.carNumber}</small>
          </div>
        </div>
      `;
    });

    container.innerHTML = appointmentsHTML;
  }

  // تحميل مواعيد التقويم
  loadCalendarAppointments() {
    // تحديث مؤشرات المواعيد
    document.querySelectorAll('.calendar-day').forEach(dayElement => {
      const dayNumber = dayElement.querySelector('.day-number');
      if (dayNumber) {
        const day = parseInt(dayNumber.textContent);
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const date = new Date(year, month, day);
        const dateString = date.toISOString().split('T')[0];
        
        if (this.hasAppointmentsOnDate(dateString)) {
          dayElement.classList.add('has-appointments');
          if (!dayElement.querySelector('.appointment-indicator')) {
            dayElement.innerHTML += '<div class="appointment-indicator"></div>';
          }
        }
      }
    });
  }
}

// إضافة أنماط CSS للتقويم
const calendarStyles = `
<style>
.calendar-modal-content {
  max-width: 800px;
  width: 90%;
}

.calendar-container {
  font-family: Arial, sans-serif;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.calendar-nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.calendar-nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.calendar-grid {
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
}

.weekday {
  padding: 12px;
  text-align: center;
  font-weight: bold;
  color: #667eea;
  border-right: 1px solid #ddd;
}

.weekday:last-child {
  border-right: none;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  min-height: 80px;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding: 8px;
  cursor: pointer;
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.calendar-day:hover {
  background: #f8f9ff;
}

.calendar-day.today {
  background: #e3f2fd;
  font-weight: bold;
}

.calendar-day.selected {
  background: #667eea;
  color: white;
}

.calendar-day.has-appointments {
  background: #fff3cd;
}

.calendar-day.has-appointments.selected {
  background: #667eea;
}

.calendar-day.empty {
  background: #f8f9fa;
  cursor: default;
}

.day-number {
  font-size: 14px;
  font-weight: 500;
}

.appointment-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  background: #f59e0b;
  border-radius: 50%;
}

.calendar-appointments {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.calendar-appointment-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin: 5px 0;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.appointment-time {
  background: #667eea;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 15px;
  min-width: 60px;
  text-align: center;
}

.appointment-details {
  flex: 1;
  font-size: 14px;
}
</style>
`;

// إضافة الأنماط إلى الصفحة
document.head.insertAdjacentHTML('beforeend', calendarStyles);

// دوال عامة للتقويم
function showCalendarView() {
  window.calendarManager.showCalendarView();
}

function hideCalendarModal() {
  const modal = document.getElementById('calendar-modal');
  if (modal) {
    modal.remove();
  }
}

// إضافة CalendarManager إلى النافذة العامة
window.CalendarManager = CalendarManager;
