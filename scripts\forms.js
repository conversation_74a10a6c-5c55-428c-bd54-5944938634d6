// إدارة النماذج والمودالات
class FormsManager {
  constructor(app) {
    this.app = app;
    this.currentModal = null;
  }

  // عرض مودال
  showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('active');
      this.currentModal = modal;
    }
  }

  // إخفاء مودال
  hideModal() {
    if (this.currentModal) {
      this.currentModal.classList.remove('active');
      this.currentModal = null;
    }
  }

  // إنشاء مودال ديناميكي
  createModal(title, content, actions = []) {
    const modalId = 'dynamic-modal-' + Date.now();
    const modalHTML = `
      <div id="${modalId}" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h3>${title}</h3>
            <button class="close-btn" onclick="formsManager.hideModal()">&times;</button>
          </div>
          <div class="modal-body">
            ${content}
          </div>
          <div class="modal-footer">
            ${actions.map(action => `<button class="${action.class}" onclick="${action.onclick}">${action.text}</button>`).join('')}
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    this.showModal(modalId);

    // إزالة المودال عند الإغلاق
    setTimeout(() => {
      const modal = document.getElementById(modalId);
      if (modal && !modal.classList.contains('active')) {
        modal.remove();
      }
    }, 1000);
  }

  // نموذج إضافة عميل جديد
  showAddCustomerForm() {
    const formContent = `
      <form id="add-customer-form">
        <div class="form-group">
          <label>الاسم:</label>
          <input type="text" name="name" required>
        </div>
        <div class="form-group">
          <label>رقم الهاتف:</label>
          <input type="tel" name="phone" required>
        </div>
        <div class="form-group">
          <label>المدينة:</label>
          <input type="text" name="city" required>
        </div>
        <div class="form-group">
          <label>نوع الخدمة:</label>
          <select name="serviceType" required>
            <option value="">اختر نوع الخدمة</option>
            <option value="غاز منزلي">غاز منزلي</option>
            <option value="غاز تجاري">غاز تجاري</option>
            <option value="غاز صناعي">غاز صناعي</option>
          </select>
        </div>
        <div class="form-group">
          <label>العنوان:</label>
          <textarea name="address" rows="3"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitCustomerForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة عميل جديد', formContent, actions);
  }

  // إرسال نموذج العميل
  submitCustomerForm() {
    const form = document.getElementById('add-customer-form');
    const formData = new FormData(form);
    
    const customerData = {
      name: formData.get('name'),
      phone: formData.get('phone'),
      city: formData.get('city'),
      serviceType: formData.get('serviceType'),
      address: formData.get('address')
    };

    if (this.validateCustomerData(customerData)) {
      this.app.addCustomer(customerData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات العميل
  validateCustomerData(data) {
    if (!data.name || !data.phone || !data.city || !data.serviceType) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }
    return true;
  }

  // نموذج إضافة بطاقة غاز جديدة
  showAddGasCardForm() {
    const formContent = `
      <form id="add-gas-card-form">
        <div class="form-group">
          <label>رقم البطاقة:</label>
          <input type="text" name="cardNumber" required>
        </div>
        <div class="form-group">
          <label>اسم العميل:</label>
          <select name="customerName" required>
            <option value="">اختر العميل</option>
            ${this.app.data.customers.map(customer => 
              `<option value="${customer.name}">${customer.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label>نوع الغاز:</label>
          <select name="gasType" required>
            <option value="">اختر نوع الغاز</option>
            <option value="غاز طبيعي">غاز طبيعي</option>
            <option value="غاز البترول المسال">غاز البترول المسال</option>
            <option value="غاز الأسيتيلين">غاز الأسيتيلين</option>
          </select>
        </div>
        <div class="form-group">
          <label>الرصيد الأولي:</label>
          <input type="number" name="balance" min="0" step="0.01" required>
        </div>
        <div class="form-group">
          <label>تاريخ الانتهاء:</label>
          <input type="date" name="expiryDate" required>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitGasCardForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة بطاقة غاز جديدة', formContent, actions);
  }

  // إرسال نموذج بطاقة الغاز
  submitGasCardForm() {
    const form = document.getElementById('add-gas-card-form');
    const formData = new FormData(form);
    
    const cardData = {
      cardNumber: formData.get('cardNumber'),
      customerName: formData.get('customerName'),
      gasType: formData.get('gasType'),
      balance: parseFloat(formData.get('balance')),
      expiryDate: formData.get('expiryDate')
    };

    if (this.validateGasCardData(cardData)) {
      this.app.addGasCard(cardData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات بطاقة الغاز
  validateGasCardData(data) {
    if (!data.cardNumber || !data.customerName || !data.gasType || !data.expiryDate) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }

    // التحقق من عدم تكرار رقم البطاقة
    const existingCard = this.app.data.gasCards.find(card => card.cardNumber === data.cardNumber);
    if (existingCard) {
      this.app.showAlert('رقم البطاقة موجود مسبقاً', 'error');
      return false;
    }

    return true;
  }

  // نموذج إضافة مورد جديد
  showAddSupplierForm() {
    const formContent = `
      <form id="add-supplier-form">
        <div class="form-group">
          <label>اسم المورد:</label>
          <input type="text" name="name" required>
        </div>
        <div class="form-group">
          <label>رقم الهاتف:</label>
          <input type="tel" name="phone" required>
        </div>
        <div class="form-group">
          <label>المدينة:</label>
          <input type="text" name="city" required>
        </div>
        <div class="form-group">
          <label>نوع المنتج:</label>
          <input type="text" name="productType" required>
        </div>
        <div class="form-group">
          <label>العنوان:</label>
          <textarea name="address" rows="3"></textarea>
        </div>
        <div class="form-group">
          <label>الحالة:</label>
          <select name="status" required>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitSupplierForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة مورد جديد', formContent, actions);
  }

  // إرسال نموذج المورد
  submitSupplierForm() {
    const form = document.getElementById('add-supplier-form');
    const formData = new FormData(form);
    
    const supplierData = {
      name: formData.get('name'),
      phone: formData.get('phone'),
      city: formData.get('city'),
      productType: formData.get('productType'),
      address: formData.get('address'),
      status: formData.get('status'),
      registrationDate: new Date().toISOString()
    };

    if (this.validateSupplierData(supplierData)) {
      this.app.addSupplier(supplierData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات المورد
  validateSupplierData(data) {
    if (!data.name || !data.phone || !data.city || !data.productType) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }
    return true;
  }

  // نموذج إضافة موعد جديد
  showAddAppointmentForm() {
    const formContent = `
      <form id="add-appointment-form">
        <div class="form-group">
          <label>اسم العميل:</label>
          <select name="customerName" required>
            <option value="">اختر العميل</option>
            ${this.app.data.customers.map(customer => 
              `<option value="${customer.name}">${customer.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label>رقم السيارة:</label>
          <input type="text" name="carNumber" required>
        </div>
        <div class="form-group">
          <label>نوع الخدمة:</label>
          <select name="serviceType" required>
            <option value="">اختر نوع الخدمة</option>
            <option value="تعبئة غاز">تعبئة غاز</option>
            <option value="صيانة">صيانة</option>
            <option value="استبدال بطاقة">استبدال بطاقة</option>
          </select>
        </div>
        <div class="form-group">
          <label>التاريخ:</label>
          <input type="date" name="date" required>
        </div>
        <div class="form-group">
          <label>الوقت:</label>
          <input type="time" name="time" required>
        </div>
        <div class="form-group">
          <label>ملاحظات:</label>
          <textarea name="notes" rows="3"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitAppointmentForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة موعد جديد', formContent, actions);
  }

  // إرسال نموذج الموعد
  submitAppointmentForm() {
    const form = document.getElementById('add-appointment-form');
    const formData = new FormData(form);
    
    const appointmentData = {
      customerName: formData.get('customerName'),
      carNumber: formData.get('carNumber'),
      serviceType: formData.get('serviceType'),
      date: formData.get('date'),
      time: formData.get('time'),
      notes: formData.get('notes')
    };

    if (this.validateAppointmentData(appointmentData)) {
      this.app.addAppointment(appointmentData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات الموعد
  validateAppointmentData(data) {
    if (!data.customerName || !data.carNumber || !data.serviceType || !data.date || !data.time) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }

    // التحقق من أن التاريخ ليس في الماضي
    const appointmentDate = new Date(data.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (appointmentDate < today) {
      this.app.showAlert('لا يمكن إضافة موعد في تاريخ سابق', 'error');
      return false;
    }

    return true;
  }

  // نموذج إضافة منتج للمخزون
  showAddInventoryForm() {
    const formContent = `
      <form id="add-inventory-form">
        <div class="form-group">
          <label>اسم المنتج:</label>
          <input type="text" name="name" required>
        </div>
        <div class="form-group">
          <label>الكمية الحالية:</label>
          <input type="number" name="currentQuantity" min="0" required>
        </div>
        <div class="form-group">
          <label>الحد الأدنى:</label>
          <input type="number" name="minQuantity" min="0" required>
        </div>
        <div class="form-group">
          <label>الوحدة:</label>
          <select name="unit" required>
            <option value="">اختر الوحدة</option>
            <option value="قطعة">قطعة</option>
            <option value="كيلو">كيلو</option>
            <option value="لتر">لتر</option>
            <option value="متر">متر</option>
          </select>
        </div>
        <div class="form-group">
          <label>الوصف:</label>
          <textarea name="description" rows="3"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitInventoryForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة منتج للمخزون', formContent, actions);
  }

  // إرسال نموذج المخزون
  submitInventoryForm() {
    const form = document.getElementById('add-inventory-form');
    const formData = new FormData(form);

    const inventoryData = {
      name: formData.get('name'),
      currentQuantity: parseInt(formData.get('currentQuantity')),
      minQuantity: parseInt(formData.get('minQuantity')),
      unit: formData.get('unit'),
      description: formData.get('description')
    };

    if (this.validateInventoryData(inventoryData)) {
      this.app.addInventoryItem(inventoryData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات المخزون
  validateInventoryData(data) {
    if (!data.name || !data.unit || data.currentQuantity < 0 || data.minQuantity < 0) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة بقيم صحيحة', 'error');
      return false;
    }
    return true;
  }

  // نموذج إضافة دين جديد
  showAddDebtForm() {
    const formContent = `
      <form id="add-debt-form">
        <div class="form-group">
          <label>اسم العميل:</label>
          <select name="customerName" required>
            <option value="">اختر العميل</option>
            ${this.app.data.customers.map(customer =>
              `<option value="${customer.name}">${customer.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label>المبلغ:</label>
          <input type="number" name="amount" min="0" step="0.01" required>
        </div>
        <div class="form-group">
          <label>تاريخ الاستحقاق:</label>
          <input type="date" name="dueDate" required>
        </div>
        <div class="form-group">
          <label>الوصف:</label>
          <textarea name="description" rows="3" required></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitDebtForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة دين جديد', formContent, actions);
  }

  // إرسال نموذج الدين
  submitDebtForm() {
    const form = document.getElementById('add-debt-form');
    const formData = new FormData(form);

    const debtData = {
      customerName: formData.get('customerName'),
      amount: parseFloat(formData.get('amount')),
      dueDate: formData.get('dueDate'),
      description: formData.get('description'),
      status: 'active'
    };

    if (this.validateDebtData(debtData)) {
      this.app.addDebt(debtData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات الدين
  validateDebtData(data) {
    if (!data.customerName || !data.amount || !data.dueDate || !data.description) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }

    // التحقق من أن تاريخ الاستحقاق ليس في الماضي
    const dueDate = new Date(data.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (dueDate < today) {
      this.app.showAlert('تاريخ الاستحقاق لا يمكن أن يكون في الماضي', 'error');
      return false;
    }

    return true;
  }

  // نموذج إضافة عملية بيع
  showNewSaleForm() {
    const formContent = `
      <form id="add-sale-form">
        <div class="form-group">
          <label>اسم العميل:</label>
          <select name="customerName" required>
            <option value="">اختر العميل</option>
            ${this.app.data.customers.map(customer =>
              `<option value="${customer.name}">${customer.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label>نوع الخدمة:</label>
          <select name="serviceType" required>
            <option value="">اختر نوع الخدمة</option>
            <option value="تعبئة غاز">تعبئة غاز</option>
            <option value="بيع أسطوانة">بيع أسطوانة</option>
            <option value="صيانة">صيانة</option>
            <option value="استبدال بطاقة">استبدال بطاقة</option>
          </select>
        </div>
        <div class="form-group">
          <label>المبلغ:</label>
          <input type="number" name="amount" min="0" step="0.01" required>
        </div>
        <div class="form-group">
          <label>ملاحظات:</label>
          <textarea name="notes" rows="3"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitSaleForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('عملية بيع جديدة', formContent, actions);
  }

  // إرسال نموذج المبيعات
  submitSaleForm() {
    const form = document.getElementById('add-sale-form');
    const formData = new FormData(form);

    const saleData = {
      customerName: formData.get('customerName'),
      serviceType: formData.get('serviceType'),
      amount: parseFloat(formData.get('amount')),
      notes: formData.get('notes')
    };

    if (this.validateSaleData(saleData)) {
      this.app.addSale(saleData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات المبيعات
  validateSaleData(data) {
    if (!data.customerName || !data.serviceType || !data.amount || data.amount <= 0) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة بقيم صحيحة', 'error');
      return false;
    }
    return true;
  }

  // نموذج إضافة عملية شراء
  showNewPurchaseForm() {
    const formContent = `
      <form id="add-purchase-form">
        <div class="form-group">
          <label>اسم المورد:</label>
          <select name="supplierName" required>
            <option value="">اختر المورد</option>
            ${this.app.data.suppliers.map(supplier =>
              `<option value="${supplier.name}">${supplier.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label>المبلغ:</label>
          <input type="number" name="amount" min="0" step="0.01" required>
        </div>
        <div class="form-group">
          <label>الوصف:</label>
          <textarea name="description" rows="3" required></textarea>
        </div>
        <div class="form-group">
          <label>ملاحظات:</label>
          <textarea name="notes" rows="2"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitPurchaseForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('عملية شراء جديدة', formContent, actions);
  }

  // إرسال نموذج المشتريات
  submitPurchaseForm() {
    const form = document.getElementById('add-purchase-form');
    const formData = new FormData(form);

    const purchaseData = {
      supplierName: formData.get('supplierName'),
      amount: parseFloat(formData.get('amount')),
      description: formData.get('description'),
      notes: formData.get('notes')
    };

    if (this.validatePurchaseData(purchaseData)) {
      this.app.addPurchase(purchaseData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات المشتريات
  validatePurchaseData(data) {
    if (!data.supplierName || !data.amount || !data.description || data.amount <= 0) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة بقيم صحيحة', 'error');
      return false;
    }
    return true;
  }

  // نموذج إضافة عملية إرسال
  showAddDispatchForm() {
    const formContent = `
      <form id="add-dispatch-form">
        <div class="form-group">
          <label>اسم السائق:</label>
          <input type="text" name="driverName" required>
        </div>
        <div class="form-group">
          <label>الوجهة:</label>
          <input type="text" name="destination" required>
        </div>
        <div class="form-group">
          <label>الحالة:</label>
          <select name="status" required>
            <option value="في الانتظار">في الانتظار</option>
            <option value="في الطريق">في الطريق</option>
            <option value="مكتمل">مكتمل</option>
            <option value="ملغي">ملغي</option>
          </select>
        </div>
        <div class="form-group">
          <label>ملاحظات:</label>
          <textarea name="notes" rows="3"></textarea>
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'formsManager.submitDispatchForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'formsManager.hideModal()'
      }
    ];

    this.createModal('إضافة عملية إرسال', formContent, actions);
  }

  // إرسال نموذج الإرسال
  submitDispatchForm() {
    const form = document.getElementById('add-dispatch-form');
    const formData = new FormData(form);

    const dispatchData = {
      driverName: formData.get('driverName'),
      destination: formData.get('destination'),
      status: formData.get('status'),
      notes: formData.get('notes')
    };

    if (this.validateDispatchData(dispatchData)) {
      this.app.addDispatch(dispatchData);
      this.hideModal();
    }
  }

  // التحقق من صحة بيانات الإرسال
  validateDispatchData(data) {
    if (!data.driverName || !data.destination || !data.status) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }
    return true;
  }
}

// دوال عامة للنماذج
function showAddCustomerForm() {
  window.formsManager.showAddCustomerForm();
}

function showAddGasCardForm() {
  window.formsManager.showAddGasCardForm();
}

function showAddSupplierForm() {
  window.formsManager.showAddSupplierForm();
}

function showAddAppointmentForm() {
  window.formsManager.showAddAppointmentForm();
}

function showAddInventoryForm() {
  window.formsManager.showAddInventoryForm();
}

function showAddDebtForm() {
  window.formsManager.showAddDebtForm();
}

function showNewSaleForm() {
  window.formsManager.showNewSaleForm();
}

function showNewPurchaseForm() {
  window.formsManager.showNewPurchaseForm();
}

function showAddDispatchForm() {
  window.formsManager.showAddDispatchForm();
}

function printCustomerCertificate(customerId) {
  window.app.printCustomerCertificate(customerId);
}

// إضافة FormsManager إلى النافذة العامة
window.FormsManager = FormsManager;
