// إدارة المستخدمين والصلاحيات
class UsersManager {
  constructor(app) {
    this.app = app;
    this.currentUser = null;
    this.userRoles = {
      admin: {
        name: 'مدير النظام',
        permissions: ['all']
      },
      manager: {
        name: 'مدير',
        permissions: ['customers', 'gasCards', 'appointments', 'suppliers', 'inventory', 'sales', 'purchases', 'debts', 'dispatch', 'reports']
      },
      employee: {
        name: 'موظف',
        permissions: ['customers', 'gasCards', 'appointments', 'sales']
      },
      viewer: {
        name: 'مشاهد',
        permissions: ['view_only']
      }
    };
    
    this.initializeDefaultUser();
  }

  // تهيئة المستخدم الافتراضي
  initializeDefaultUser() {
    if (!this.app.data.users) {
      this.app.data.users = [];
    }

    // إنشاء مستخدم افتراضي إذا لم يوجد
    if (this.app.data.users.length === 0) {
      const defaultUser = {
        id: 1,
        username: 'admin',
        password: 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
        fullName: 'مدير النظام',
        role: 'admin',
        email: '<EMAIL>',
        phone: '',
        isActive: true,
        createdDate: new Date().toISOString(),
        lastLogin: null
      };
      
      this.app.data.users.push(defaultUser);
      this.app.saveData();
    }

    // تسجيل دخول المستخدم الافتراضي
    this.currentUser = this.app.data.users[0];
  }

  // عرض نموذج تسجيل الدخول
  showLoginForm() {
    const loginHTML = `
      <div class="login-container">
        <div class="login-form">
          <div class="login-header">
            <h2>تسجيل الدخول</h2>
            <p>نظام إدارة مؤسسة وقود المستقبل</p>
          </div>
          <form id="login-form">
            <div class="form-group">
              <label>اسم المستخدم:</label>
              <input type="text" name="username" required>
            </div>
            <div class="form-group">
              <label>كلمة المرور:</label>
              <input type="password" name="password" required>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn-primary">دخول</button>
            </div>
          </form>
          <div class="login-footer">
            <p><small>المستخدم الافتراضي: admin / admin123</small></p>
          </div>
        </div>
      </div>
    `;

    document.body.innerHTML = loginHTML;
    this.setupLoginForm();
  }

  // إعداد نموذج تسجيل الدخول
  setupLoginForm() {
    const form = document.getElementById('login-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      const formData = new FormData(form);
      const username = formData.get('username');
      const password = formData.get('password');
      
      this.login(username, password);
    });
  }

  // تسجيل الدخول
  login(username, password) {
    const user = this.app.data.users.find(u => 
      u.username === username && u.password === password && u.isActive
    );

    if (user) {
      this.currentUser = user;
      user.lastLogin = new Date().toISOString();
      this.app.saveData();
      
      // إعادة تحميل التطبيق
      location.reload();
    } else {
      alert('اسم المستخدم أو كلمة المرور غير صحيحة');
    }
  }

  // تسجيل الخروج
  logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      this.currentUser = null;
      this.showLoginForm();
    }
  }

  // التحقق من الصلاحيات
  hasPermission(permission) {
    if (!this.currentUser) return false;
    
    const userRole = this.userRoles[this.currentUser.role];
    if (!userRole) return false;
    
    return userRole.permissions.includes('all') || userRole.permissions.includes(permission);
  }

  // عرض إدارة المستخدمين
  showUsersManagement() {
    if (!this.hasPermission('all')) {
      this.app.showAlert('ليس لديك صلاحية للوصول إلى إدارة المستخدمين', 'error');
      return;
    }

    const usersHTML = this.generateUsersManagementHTML();
    
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.id = 'users-modal';
    modal.innerHTML = `
      <div class="modal-content users-modal-content">
        <div class="modal-header">
          <h3>إدارة المستخدمين</h3>
          <button class="close-btn" onclick="hideUsersModal()">&times;</button>
        </div>
        <div class="modal-body">
          ${usersHTML}
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // توليد HTML إدارة المستخدمين
  generateUsersManagementHTML() {
    let html = `
      <div class="users-management">
        <div class="users-actions">
          <button class="btn-primary" onclick="usersManager.showAddUserForm()">إضافة مستخدم جديد</button>
        </div>
        <div class="users-list">
          <table class="users-table">
            <thead>
              <tr>
                <th>الاسم الكامل</th>
                <th>اسم المستخدم</th>
                <th>الدور</th>
                <th>البريد الإلكتروني</th>
                <th>الحالة</th>
                <th>آخر دخول</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
    `;

    this.app.data.users.forEach(user => {
      const roleName = this.userRoles[user.role]?.name || user.role;
      const statusClass = user.isActive ? 'status-active' : 'status-expired';
      const statusText = user.isActive ? 'نشط' : 'معطل';
      const lastLogin = user.lastLogin ? formatDate(user.lastLogin) : 'لم يسجل دخول';

      html += `
        <tr>
          <td>${user.fullName}</td>
          <td>${user.username}</td>
          <td>${roleName}</td>
          <td>${user.email}</td>
          <td><span class="${statusClass}">${statusText}</span></td>
          <td>${lastLogin}</td>
          <td>
            <button class="action-btn edit" onclick="usersManager.editUser(${user.id})" title="تعديل">✏️</button>
            ${user.id !== this.currentUser.id ? `
              <button class="action-btn ${user.isActive ? 'delete' : 'edit'}" 
                      onclick="usersManager.toggleUserStatus(${user.id})" 
                      title="${user.isActive ? 'تعطيل' : 'تفعيل'}">
                ${user.isActive ? '🚫' : '✅'}
              </button>
            ` : ''}
          </td>
        </tr>
      `;
    });

    html += `
            </tbody>
          </table>
        </div>
      </div>
    `;

    return html;
  }

  // عرض نموذج إضافة مستخدم
  showAddUserForm() {
    const formContent = `
      <form id="add-user-form">
        <div class="form-group">
          <label>الاسم الكامل:</label>
          <input type="text" name="fullName" required>
        </div>
        <div class="form-group">
          <label>اسم المستخدم:</label>
          <input type="text" name="username" required>
        </div>
        <div class="form-group">
          <label>كلمة المرور:</label>
          <input type="password" name="password" required>
        </div>
        <div class="form-group">
          <label>تأكيد كلمة المرور:</label>
          <input type="password" name="confirmPassword" required>
        </div>
        <div class="form-group">
          <label>الدور:</label>
          <select name="role" required>
            <option value="">اختر الدور</option>
            <option value="admin">مدير النظام</option>
            <option value="manager">مدير</option>
            <option value="employee">موظف</option>
            <option value="viewer">مشاهد</option>
          </select>
        </div>
        <div class="form-group">
          <label>البريد الإلكتروني:</label>
          <input type="email" name="email">
        </div>
        <div class="form-group">
          <label>رقم الهاتف:</label>
          <input type="tel" name="phone">
        </div>
      </form>
    `;

    const actions = [
      {
        text: 'إضافة',
        class: 'btn-primary',
        onclick: 'usersManager.submitUserForm()'
      },
      {
        text: 'إلغاء',
        class: 'btn-secondary',
        onclick: 'usersManager.hideUserForm()'
      }
    ];

    this.app.formsManager.createModal('إضافة مستخدم جديد', formContent, actions);
  }

  // إرسال نموذج المستخدم
  submitUserForm() {
    const form = document.getElementById('add-user-form');
    const formData = new FormData(form);
    
    const userData = {
      fullName: formData.get('fullName'),
      username: formData.get('username'),
      password: formData.get('password'),
      confirmPassword: formData.get('confirmPassword'),
      role: formData.get('role'),
      email: formData.get('email'),
      phone: formData.get('phone')
    };

    if (this.validateUserData(userData)) {
      this.addUser(userData);
      this.app.formsManager.hideModal();
      this.showUsersManagement();
    }
  }

  // التحقق من صحة بيانات المستخدم
  validateUserData(data) {
    if (!data.fullName || !data.username || !data.password || !data.role) {
      this.app.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
      return false;
    }

    if (data.password !== data.confirmPassword) {
      this.app.showAlert('كلمة المرور وتأكيدها غير متطابقتين', 'error');
      return false;
    }

    if (this.app.data.users.some(u => u.username === data.username)) {
      this.app.showAlert('اسم المستخدم موجود مسبقاً', 'error');
      return false;
    }

    return true;
  }

  // إضافة مستخدم جديد
  addUser(userData) {
    const user = {
      id: Date.now(),
      fullName: userData.fullName,
      username: userData.username,
      password: userData.password, // في التطبيق الحقيقي يجب تشفير كلمة المرور
      role: userData.role,
      email: userData.email || '',
      phone: userData.phone || '',
      isActive: true,
      createdDate: new Date().toISOString(),
      lastLogin: null
    };

    this.app.data.users.push(user);
    this.app.saveData();
    this.app.showAlert('تم إضافة المستخدم بنجاح', 'success');
  }

  // تبديل حالة المستخدم
  toggleUserStatus(userId) {
    const user = this.app.data.users.find(u => u.id === userId);
    if (user && user.id !== this.currentUser.id) {
      user.isActive = !user.isActive;
      this.app.saveData();
      this.showUsersManagement();
      this.app.showAlert(`تم ${user.isActive ? 'تفعيل' : 'تعطيل'} المستخدم`, 'success');
    }
  }

  // إخفاء نموذج المستخدم
  hideUserForm() {
    this.app.formsManager.hideModal();
  }

  // الحصول على معلومات المستخدم الحالي
  getCurrentUser() {
    return this.currentUser;
  }

  // التحقق من تسجيل الدخول
  isLoggedIn() {
    return this.currentUser !== null;
  }
}

// دوال عامة لإدارة المستخدمين
function showUsersManagement() {
  window.usersManager.showUsersManagement();
}

function hideUsersModal() {
  const modal = document.getElementById('users-modal');
  if (modal) {
    modal.remove();
  }
}

function logout() {
  window.usersManager.logout();
}

// إضافة UsersManager إلى النافذة العامة
window.UsersManager = UsersManager;
