// إدارة التقارير والطباعة
class ReportsManager {
  constructor(app) {
    this.app = app;
  }

  // تقرير المخزون
  generateInventoryReport() {
    const inventory = this.app.data.inventory;
    const reportWindow = window.open('', '_blank');
    const reportHTML = this.generateInventoryReportHTML(inventory);
    
    reportWindow.document.write(reportHTML);
    reportWindow.document.close();
    reportWindow.print();
  }

  // توليد HTML تقرير المخزون
  generateInventoryReportHTML(inventory) {
    const lowStockItems = inventory.filter(item => item.currentQuantity <= item.minQuantity);
    
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>تقرير المخزون</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #667eea; padding-bottom: 20px; }
          .title { font-size: 24px; color: #667eea; margin-bottom: 10px; }
          .date { color: #666; }
          .summary { display: flex; justify-content: space-around; margin: 20px 0; }
          .summary-item { text-align: center; padding: 15px; background: #f8f9ff; border-radius: 10px; }
          .summary-number { font-size: 24px; font-weight: bold; color: #667eea; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }
          th { background: #667eea; color: white; }
          .low-stock { background: #fff3cd; }
          .alert { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 class="title">تقرير المخزون</h1>
          <p class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
          <p>${this.app.data.settings.companyName}</p>
        </div>
        
        <div class="summary">
          <div class="summary-item">
            <div class="summary-number">${inventory.length}</div>
            <div>إجمالي المنتجات</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${lowStockItems.length}</div>
            <div>منتجات منخفضة المخزون</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${inventory.reduce((sum, item) => sum + item.currentQuantity, 0)}</div>
            <div>إجمالي الكمية</div>
          </div>
        </div>

        ${lowStockItems.length > 0 ? `
          <div class="alert">
            <strong>تنبيه:</strong> يوجد ${lowStockItems.length} منتج منخفض المخزون يحتاج إلى إعادة تموين
          </div>
        ` : ''}

        <table>
          <thead>
            <tr>
              <th>اسم المنتج</th>
              <th>الكمية الحالية</th>
              <th>الحد الأدنى</th>
              <th>الوحدة</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${inventory.map(item => `
              <tr class="${item.currentQuantity <= item.minQuantity ? 'low-stock' : ''}">
                <td>${item.name}</td>
                <td>${item.currentQuantity}</td>
                <td>${item.minQuantity}</td>
                <td>${item.unit}</td>
                <td>${item.currentQuantity <= item.minQuantity ? 'مخزون منخفض' : 'متوفر'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
  }

  // تقرير المبيعات
  generateSalesReport() {
    const sales = this.app.data.sales;
    const reportWindow = window.open('', '_blank');
    const reportHTML = this.generateSalesReportHTML(sales);
    
    reportWindow.document.write(reportHTML);
    reportWindow.document.close();
    reportWindow.print();
  }

  // توليد HTML تقرير المبيعات
  generateSalesReportHTML(sales) {
    const today = new Date();
    const thisMonth = sales.filter(sale => {
      const saleDate = new Date(sale.date);
      return saleDate.getMonth() === today.getMonth() && saleDate.getFullYear() === today.getFullYear();
    });

    const totalSales = sales.reduce((sum, sale) => sum + sale.amount, 0);
    const monthlyTotal = thisMonth.reduce((sum, sale) => sum + sale.amount, 0);

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>تقرير المبيعات</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #10b981; padding-bottom: 20px; }
          .title { font-size: 24px; color: #10b981; margin-bottom: 10px; }
          .summary { display: flex; justify-content: space-around; margin: 20px 0; }
          .summary-item { text-align: center; padding: 15px; background: #f0fdf4; border-radius: 10px; }
          .summary-number { font-size: 24px; font-weight: bold; color: #10b981; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }
          th { background: #10b981; color: white; }
          .total-row { background: #f0fdf4; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 class="title">تقرير المبيعات</h1>
          <p class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
          <p>${this.app.data.settings.companyName}</p>
        </div>
        
        <div class="summary">
          <div class="summary-item">
            <div class="summary-number">${sales.length}</div>
            <div>إجمالي العمليات</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${thisMonth.length}</div>
            <div>عمليات هذا الشهر</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${totalSales.toFixed(2)} ريال</div>
            <div>إجمالي المبيعات</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${monthlyTotal.toFixed(2)} ريال</div>
            <div>مبيعات هذا الشهر</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>رقم الفاتورة</th>
              <th>اسم العميل</th>
              <th>نوع الخدمة</th>
              <th>المبلغ</th>
              <th>التاريخ</th>
            </tr>
          </thead>
          <tbody>
            ${sales.map(sale => `
              <tr>
                <td>${sale.invoiceNumber}</td>
                <td>${sale.customerName}</td>
                <td>${sale.serviceType}</td>
                <td>${sale.amount.toFixed(2)} ريال</td>
                <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
              </tr>
            `).join('')}
            <tr class="total-row">
              <td colspan="3">الإجمالي</td>
              <td>${totalSales.toFixed(2)} ريال</td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </body>
      </html>
    `;
  }

  // تقرير الديون
  generateDebtsReport() {
    const debts = this.app.data.debts;
    const reportWindow = window.open('', '_blank');
    const reportHTML = this.generateDebtsReportHTML(debts);
    
    reportWindow.document.write(reportHTML);
    reportWindow.document.close();
    reportWindow.print();
  }

  // توليد HTML تقرير الديون
  generateDebtsReportHTML(debts) {
    const activeDebts = debts.filter(debt => debt.status === 'active');
    const overdueDebts = debts.filter(debt => debt.status === 'active' && new Date(debt.dueDate) < new Date());
    const paidDebts = debts.filter(debt => debt.status === 'paid');

    const totalActiveAmount = activeDebts.reduce((sum, debt) => sum + debt.amount, 0);
    const totalOverdueAmount = overdueDebts.reduce((sum, debt) => sum + debt.amount, 0);

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>تقرير الديون</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #f59e0b; padding-bottom: 20px; }
          .title { font-size: 24px; color: #f59e0b; margin-bottom: 10px; }
          .summary { display: flex; justify-content: space-around; margin: 20px 0; }
          .summary-item { text-align: center; padding: 15px; background: #fffbeb; border-radius: 10px; }
          .summary-number { font-size: 24px; font-weight: bold; color: #f59e0b; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }
          th { background: #f59e0b; color: white; }
          .overdue { background: #fee2e2; }
          .paid { background: #dcfce7; }
          .alert { background: #fee2e2; color: #991b1b; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 class="title">تقرير الديون</h1>
          <p class="date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
          <p>${this.app.data.settings.companyName}</p>
        </div>
        
        <div class="summary">
          <div class="summary-item">
            <div class="summary-number">${activeDebts.length}</div>
            <div>ديون نشطة</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${overdueDebts.length}</div>
            <div>ديون متأخرة</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${totalActiveAmount.toFixed(2)} ريال</div>
            <div>إجمالي الديون النشطة</div>
          </div>
          <div class="summary-item">
            <div class="summary-number">${totalOverdueAmount.toFixed(2)} ريال</div>
            <div>إجمالي الديون المتأخرة</div>
          </div>
        </div>

        ${overdueDebts.length > 0 ? `
          <div class="alert">
            <strong>تنبيه:</strong> يوجد ${overdueDebts.length} دين متأخر بقيمة ${totalOverdueAmount.toFixed(2)} ريال
          </div>
        ` : ''}

        <table>
          <thead>
            <tr>
              <th>اسم العميل</th>
              <th>المبلغ</th>
              <th>تاريخ الاستحقاق</th>
              <th>الحالة</th>
              <th>الوصف</th>
            </tr>
          </thead>
          <tbody>
            ${debts.map(debt => {
              const isOverdue = debt.status === 'active' && new Date(debt.dueDate) < new Date();
              const rowClass = debt.status === 'paid' ? 'paid' : (isOverdue ? 'overdue' : '');
              const statusText = debt.status === 'paid' ? 'مدفوع' : (isOverdue ? 'متأخر' : 'نشط');
              
              return `
                <tr class="${rowClass}">
                  <td>${debt.customerName}</td>
                  <td>${debt.amount.toFixed(2)} ريال</td>
                  <td>${new Date(debt.dueDate).toLocaleDateString('ar-SA')}</td>
                  <td>${statusText}</td>
                  <td>${debt.description}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
  }

  // طباعة فاتورة مبيعات
  printSaleInvoice(saleId) {
    const sale = this.app.data.sales.find(s => s.id === saleId);
    if (!sale) return;

    const invoiceWindow = window.open('', '_blank');
    const invoiceHTML = this.generateSaleInvoiceHTML(sale);
    
    invoiceWindow.document.write(invoiceHTML);
    invoiceWindow.document.close();
    invoiceWindow.print();
  }

  // توليد HTML فاتورة المبيعات
  generateSaleInvoiceHTML(sale) {
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة مبيعات - ${sale.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .invoice-header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
          .company-name { font-size: 24px; color: #10b981; margin-bottom: 10px; }
          .invoice-title { font-size: 20px; margin: 20px 0; }
          .invoice-details { display: flex; justify-content: space-between; margin: 20px 0; }
          .detail-section { flex: 1; }
          .detail-section h3 { color: #10b981; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .invoice-items { margin: 30px 0; }
          table { width: 100%; border-collapse: collapse; }
          th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }
          th { background: #10b981; color: white; }
          .total-section { text-align: left; margin-top: 20px; }
          .total-amount { font-size: 24px; font-weight: bold; color: #10b981; }
          .footer { text-align: center; margin-top: 40px; border-top: 1px solid #ddd; padding-top: 20px; }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <h1 class="company-name">${this.app.data.settings.companyName}</h1>
          <p>${this.app.data.settings.companyAddress || ''}</p>
          <p>هاتف: ${this.app.data.settings.companyPhone || ''}</p>
          <h2 class="invoice-title">فاتورة مبيعات</h2>
        </div>

        <div class="invoice-details">
          <div class="detail-section">
            <h3>تفاصيل الفاتورة</h3>
            <p><strong>رقم الفاتورة:</strong> ${sale.invoiceNumber}</p>
            <p><strong>التاريخ:</strong> ${new Date(sale.date).toLocaleDateString('ar-SA')}</p>
            <p><strong>الوقت:</strong> ${new Date(sale.date).toLocaleTimeString('ar-SA')}</p>
          </div>
          <div class="detail-section">
            <h3>بيانات العميل</h3>
            <p><strong>اسم العميل:</strong> ${sale.customerName}</p>
            <p><strong>نوع الخدمة:</strong> ${sale.serviceType}</p>
          </div>
        </div>

        <div class="invoice-items">
          <table>
            <thead>
              <tr>
                <th>الوصف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>${sale.serviceType}</td>
                <td>1</td>
                <td>${sale.amount.toFixed(2)} ريال</td>
                <td>${sale.amount.toFixed(2)} ريال</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="total-section">
          <p class="total-amount">الإجمالي: ${sale.amount.toFixed(2)} ريال</p>
        </div>

        <div class="footer">
          <p>شكراً لتعاملكم معنا</p>
          <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
      </body>
      </html>
    `;
  }
}

// دوال عامة للتقارير
function generateInventoryReport() {
  window.reportsManager.generateInventoryReport();
}

function generateSalesReport() {
  window.reportsManager.generateSalesReport();
}

function generateDebtsReport() {
  window.reportsManager.generateDebtsReport();
}

function printSaleInvoice(saleId) {
  window.reportsManager.printSaleInvoice(saleId);
}

// إضافة ReportsManager إلى النافذة العامة
window.ReportsManager = ReportsManager;
