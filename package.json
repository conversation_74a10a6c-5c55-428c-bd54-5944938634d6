{"name": "fuel-management-app", "version": "1.0.0", "description": "نظام إدارة مؤسسة وقود المستقبل - Future Fuel Management System", "main": "main.js", "author": "Future Fuel Management", "license": "MIT", "scripts": {"start": "electron .", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "build": {"appId": "com.fuelfuture.management", "productName": "نظام إدارة مؤسسة وقود المستقبل", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^30.0.0", "electron-builder": "^24.0.0"}}