// نظام إدارة مؤسسة وقود المستقبل
class FuelManagementApp {
  constructor() {
    this.currentLanguage = 'ar';
    this.currentPage = 'dashboard';
    this.translations = {};
    this.data = {
      customers: [],
      suppliers: [],
      gasCards: [],
      appointments: [],
      inventory: [],
      sales: [],
      purchases: [],
      debts: [],
      dispatch: [],
      settings: {
        companyName: 'مؤسسة وقود المستقبل',
        language: 'ar',
        currency: 'SAR'
      }
    };
    
    this.init();
  }

  async init() {
    await this.loadLanguage(this.currentLanguage);
    this.loadData();
    this.setupEventListeners();
    this.updateUI();
    this.showPage('dashboard');
    this.loadDashboardData();
  }

  // تحميل ملف اللغة
  async loadLanguage(lang) {
    try {
      this.translations = await window.electronAPI.loadLanguage(lang);
      this.currentLanguage = lang;
      
      // تحديث اتجاه الصفحة
      document.body.className = lang === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
      
      this.updateTexts();
    } catch (error) {
      console.error('Error loading language:', error);
    }
  }

  // تحديث النصوص في الواجهة
  updateTexts() {
    if (!this.translations) return;

    // تحديث عنوان التطبيق
    document.querySelector('header h1').textContent = this.translations.app.title;

    // تحديث أزرار التنقل
    const navButtons = document.querySelectorAll('nav button');
    const navKeys = ['home', 'gasCards', 'appointments', 'customers', 'suppliers', 'inventory', 'sales', 'purchases', 'debts', 'dispatch', 'settings'];
    
    navButtons.forEach((button, index) => {
      if (navKeys[index] && this.translations.nav[navKeys[index]]) {
        const icon = button.textContent.split(' ')[0];
        button.textContent = `${icon} ${this.translations.nav[navKeys[index]]}`;
      }
    });

    // تحديث عناوين الصفحات
    document.querySelectorAll('.page h2').forEach(title => {
      const pageId = title.closest('.page').id;
      if (this.translations[pageId] && this.translations[pageId].title) {
        title.textContent = this.translations[pageId].title;
      }
    });
  }

  // إعداد مستمعي الأحداث
  setupEventListeners() {
    // أزرار التنقل
    const navButtons = document.querySelectorAll('nav button');
    const pages = ['dashboard', 'gas-cards', 'appointments', 'customers', 'suppliers', 'inventory', 'sales', 'purchases', 'debts', 'dispatch', 'settings'];
    
    navButtons.forEach((button, index) => {
      button.addEventListener('click', () => {
        this.showPage(pages[index]);
        this.setActiveNavButton(button);
      });
    });

    // تحديث البيانات كل 30 ثانية
    setInterval(() => {
      this.loadDashboardData();
    }, 30000);
  }

  // عرض صفحة معينة
  showPage(pageId) {
    // إخفاء جميع الصفحات
    document.querySelectorAll('.page').forEach(page => {
      page.classList.remove('active');
    });

    // عرض الصفحة المطلوبة
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
      targetPage.classList.add('active');
      this.currentPage = pageId;
      
      // تحميل بيانات الصفحة
      this.loadPageData(pageId);
    }
  }

  // تعيين الزر النشط في التنقل
  setActiveNavButton(activeButton) {
    document.querySelectorAll('nav button').forEach(button => {
      button.classList.remove('active');
    });
    activeButton.classList.add('active');
  }

  // تحميل بيانات الصفحة
  loadPageData(pageId) {
    if (this.dataManager) {
      switch(pageId) {
        case 'dashboard':
          this.loadDashboardData();
          break;
        case 'gas-cards':
          this.dataManager.loadGasCardsData();
          break;
        case 'customers':
          this.dataManager.loadCustomersData();
          break;
        case 'suppliers':
          this.dataManager.loadSuppliersData();
          break;
        case 'appointments':
          this.dataManager.loadAppointmentsData();
          break;
        case 'inventory':
          this.dataManager.loadInventoryData();
          break;
        case 'sales':
          this.dataManager.loadSalesData();
          break;
        case 'purchases':
          this.dataManager.loadPurchasesData();
          break;
        case 'debts':
          this.dataManager.loadDebtsData();
          break;
        case 'dispatch':
          this.dataManager.loadDispatchData();
          break;
        case 'settings':
          this.dataManager.loadSettingsData();
          break;
      }
    }
  }

  // تحميل بيانات لوحة التحكم
  loadDashboardData() {
    // تحديث إحصائيات البطاقات
    document.getElementById('gas-cards-count').textContent = this.data.gasCards.length;
    document.getElementById('appointments-count').textContent = this.getTodayAppointments().length;
    document.getElementById('customers-count').textContent = this.data.customers.length;
    document.getElementById('suppliers-count').textContent = this.data.suppliers.filter(s => s.status === 'active').length;
  }

  // الحصول على مواعيد اليوم
  getTodayAppointments() {
    const today = new Date().toDateString();
    return this.data.appointments.filter(appointment => 
      new Date(appointment.date).toDateString() === today
    );
  }

  // تحميل البيانات من التخزين المحلي
  loadData() {
    try {
      const savedData = window.electronAPI.loadData('app-data.json');
      if (savedData) {
        this.data = { ...this.data, ...savedData };
      } else {
        // تحميل البيانات التجريبية إذا لم توجد بيانات محفوظة
        this.loadSampleData();
      }
    } catch (error) {
      console.error('Error loading data:', error);
      // تحميل البيانات التجريبية في حالة الخطأ
      this.loadSampleData();
    }
  }

  // تحميل البيانات التجريبية
  async loadSampleData() {
    try {
      const response = await fetch('data/sample-data.json');
      const sampleData = await response.json();
      this.data = { ...this.data, ...sampleData };
      this.saveData();
    } catch (error) {
      console.error('Error loading sample data:', error);
    }
  }

  // حفظ البيانات
  saveData() {
    try {
      window.electronAPI.saveData('app-data.json', this.data);
    } catch (error) {
      console.error('Error saving data:', error);
    }
  }

  // تحديث واجهة المستخدم
  updateUI() {
    this.updateTexts();
  }

  // إضافة عميل جديد
  addCustomer(customerData) {
    const customer = {
      id: Date.now(),
      ...customerData,
      registrationDate: new Date().toISOString()
    };
    
    this.data.customers.push(customer);
    this.saveData();
    this.loadCustomersData();
    this.showAlert('تم إضافة العميل بنجاح', 'success');
  }

  // إضافة بطاقة غاز جديدة
  addGasCard(cardData) {
    const card = {
      id: Date.now(),
      ...cardData,
      createdDate: new Date().toISOString()
    };

    this.data.gasCards.push(card);
    this.saveData();
    if (this.dataManager) {
      this.dataManager.loadGasCardsData();
    }
    this.showAlert('تم إضافة البطاقة بنجاح', 'success');
  }

  // إضافة مورد جديد
  addSupplier(supplierData) {
    const supplier = {
      id: Date.now(),
      ...supplierData
    };

    this.data.suppliers.push(supplier);
    this.saveData();
    if (this.dataManager) {
      this.dataManager.loadSuppliersData();
    }
    this.showAlert('تم إضافة المورد بنجاح', 'success');
  }

  // إضافة موعد جديد
  addAppointment(appointmentData) {
    const appointment = {
      id: Date.now(),
      ...appointmentData
    };

    this.data.appointments.push(appointment);
    this.saveData();
    if (this.dataManager) {
      this.dataManager.loadAppointmentsData();
    }
    this.showAlert('تم إضافة الموعد بنجاح', 'success');
  }

  // عرض تنبيه
  showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
      alertDiv.remove();
    }, 3000);
  }

  // طباعة شهادة العميل
  printCustomerCertificate(customerId) {
    const customer = this.data.customers.find(c => c.id === customerId);
    if (!customer) return;

    const certificateWindow = window.open('', '_blank');
    const certificateHTML = this.generateCertificateHTML(customer);
    
    certificateWindow.document.write(certificateHTML);
    certificateWindow.document.close();
    certificateWindow.print();
  }

  // توليد HTML للشهادة
  generateCertificateHTML(customer) {
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>شهادة العميل</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 40px; text-align: center; }
          .certificate { border: 3px solid #667eea; padding: 40px; border-radius: 15px; }
          .title { font-size: 32px; color: #667eea; margin-bottom: 20px; }
          .content { font-size: 18px; line-height: 2; margin: 30px 0; }
          .footer { margin-top: 40px; border-top: 2px solid #ccc; padding-top: 20px; }
        </style>
      </head>
      <body>
        <div class="certificate">
          <h1 class="title">شهادة عضوية</h1>
          <h2>${this.data.settings.companyName}</h2>
          <div class="content">
            <p>نشهد بأن السيد/ة: <strong>${customer.name}</strong></p>
            <p>رقم الهاتف: <strong>${customer.phone}</strong></p>
            <p>المدينة: <strong>${customer.city}</strong></p>
            <p>نوع الخدمة: <strong>${customer.serviceType}</strong></p>
            <p>مسجل لدينا منذ: <strong>${new Date(customer.registrationDate).toLocaleDateString('ar-SA')}</strong></p>
          </div>
          <div class="footer">
            <p>تاريخ الإصدار: ${new Date().toLocaleDateString('ar-SA')}</p>
            <p>ختم المؤسسة</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  window.app = new FuelManagementApp();
});

// دوال مساعدة عامة
function formatDate(date) {
  return new Date(date).toLocaleDateString('ar-SA');
}

function formatCurrency(amount) {
  return `${amount} ريال`;
}

function calculateDaysRemaining(expiryDate) {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const diffTime = expiry - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}
