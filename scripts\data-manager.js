// إدارة البيانات والجداول
class DataManager {
  constructor(app) {
    this.app = app;
  }

  // تحميل بيانات بطاقات الغاز
  loadGasCardsData() {
    const tableBody = document.querySelector('#gas-cards-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    this.app.data.gasCards.forEach(card => {
      const remainingDays = calculateDaysRemaining(card.expiryDate);
      let statusClass = 'status-active';
      let statusText = 'نشطة';

      if (remainingDays < 0) {
        statusClass = 'status-expired';
        statusText = 'منتهية';
      } else if (remainingDays <= 7) {
        statusClass = 'status-expiring';
        statusText = 'تنتهي قريباً';
      }

      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${card.cardNumber}</td>
        <td>${card.customerName}</td>
        <td>${card.gasType}</td>
        <td>${card.balance} ريال</td>
        <td>${formatDate(card.createdDate)}</td>
        <td><span class="${statusClass}">${statusText}</span></td>
        <td>
          <button class="action-btn edit" onclick="editGasCard(${card.id})" title="تعديل">✏️</button>
          <button class="action-btn delete" onclick="deleteGasCard(${card.id})" title="حذف">🗑️</button>
        </td>
      `;
      tableBody.appendChild(row);
    });
  }

  // تحميل بيانات العملاء
  loadCustomersData() {
    const tableBody = document.querySelector('#customers-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    this.app.data.customers.forEach(customer => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${customer.name}</td>
        <td>${customer.phone}</td>
        <td>${customer.city}</td>
        <td>${customer.serviceType}</td>
        <td>${formatDate(customer.registrationDate)}</td>
        <td>
          <button class="action-btn edit" onclick="editCustomer(${customer.id})" title="تعديل">✏️</button>
          <button class="action-btn print" onclick="printCustomerCertificate(${customer.id})" title="طباعة الشهادة">🖨️</button>
          <button class="action-btn delete" onclick="deleteCustomer(${customer.id})" title="حذف">🗑️</button>
        </td>
      `;
      tableBody.appendChild(row);
    });
  }

  // تحميل بيانات الموردين
  loadSuppliersData() {
    const tableBody = document.querySelector('#suppliers-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    this.app.data.suppliers.forEach(supplier => {
      const statusClass = supplier.status === 'active' ? 'status-active' : 'status-expired';
      const statusText = supplier.status === 'active' ? 'نشط' : 'غير نشط';

      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${supplier.name}</td>
        <td>${supplier.phone}</td>
        <td>${supplier.city}</td>
        <td>${supplier.productType}</td>
        <td>${formatDate(supplier.registrationDate)}</td>
        <td><span class="${statusClass}">${statusText}</span></td>
        <td>
          <button class="action-btn edit" onclick="editSupplier(${supplier.id})" title="تعديل">✏️</button>
          <button class="action-btn delete" onclick="deleteSupplier(${supplier.id})" title="حذف">🗑️</button>
        </td>
      `;
      tableBody.appendChild(row);
    });

    // تحديث إحصائيات الموردين
    const activeSuppliers = this.app.data.suppliers.filter(s => s.status === 'active').length;
    const totalSuppliers = this.app.data.suppliers.length;
    
    document.getElementById('active-suppliers-count').textContent = activeSuppliers;
    document.getElementById('total-suppliers-count').textContent = totalSuppliers;
  }

  // تحميل بيانات المواعيد
  loadAppointmentsData() {
    const container = document.getElementById('appointments-list');
    if (!container) return;

    container.innerHTML = '';

    // ترتيب المواعيد حسب التاريخ
    const sortedAppointments = this.app.data.appointments.sort((a, b) => new Date(a.date) - new Date(b.date));

    sortedAppointments.forEach(appointment => {
      const appointmentDiv = document.createElement('div');
      appointmentDiv.className = 'appointment-item';
      appointmentDiv.innerHTML = `
        <div class="appointment-header">
          <h4>${appointment.customerName}</h4>
          <span class="appointment-time">${appointment.time}</span>
        </div>
        <div class="appointment-details">
          <p><strong>رقم السيارة:</strong> ${appointment.carNumber}</p>
          <p><strong>نوع الخدمة:</strong> ${appointment.serviceType}</p>
          <p><strong>التاريخ:</strong> ${formatDate(appointment.date)}</p>
        </div>
        <div class="appointment-actions">
          <button class="action-btn edit" onclick="editAppointment(${appointment.id})">تعديل</button>
          <button class="action-btn delete" onclick="deleteAppointment(${appointment.id})">حذف</button>
        </div>
      `;
      container.appendChild(appointmentDiv);
    });
  }

  // تحميل بيانات المخزون
  loadInventoryData() {
    const container = document.getElementById('inventory-list');
    if (!container) return;

    container.innerHTML = '';

    let totalProducts = 0;
    let lowStockProducts = 0;

    this.app.data.inventory.forEach(item => {
      totalProducts++;
      const isLowStock = item.currentQuantity <= item.minQuantity;
      if (isLowStock) lowStockProducts++;

      const itemDiv = document.createElement('div');
      itemDiv.className = `inventory-item ${isLowStock ? 'low-stock' : ''}`;
      itemDiv.innerHTML = `
        <div class="item-header">
          <h4>${item.name}</h4>
          <span class="item-status ${isLowStock ? 'status-warning' : 'status-active'}">
            ${isLowStock ? 'مخزون منخفض' : 'متوفر'}
          </span>
        </div>
        <div class="item-details">
          <p><strong>الكمية الحالية:</strong> ${item.currentQuantity}</p>
          <p><strong>الحد الأدنى:</strong> ${item.minQuantity}</p>
          <p><strong>الوحدة:</strong> ${item.unit}</p>
        </div>
        <div class="item-actions">
          <button class="action-btn edit" onclick="editInventoryItem(${item.id})">تعديل</button>
          <button class="action-btn delete" onclick="deleteInventoryItem(${item.id})">حذف</button>
        </div>
      `;
      container.appendChild(itemDiv);
    });

    // تحديث الإحصائيات
    document.getElementById('total-products').textContent = totalProducts;
    document.getElementById('low-stock-products').textContent = lowStockProducts;
  }

  // تحميل بيانات المبيعات
  loadSalesData() {
    const container = document.getElementById('sales-list');
    if (!container) return;

    container.innerHTML = '';

    // حساب إجمالي مبيعات اليوم
    const today = new Date().toDateString();
    const todaySales = this.app.data.sales.filter(sale => 
      new Date(sale.date).toDateString() === today
    );

    const dailyTotal = todaySales.reduce((sum, sale) => sum + sale.amount, 0);
    document.getElementById('daily-sales-total').textContent = formatCurrency(dailyTotal);
    document.getElementById('daily-sales-count').textContent = todaySales.length;

    // عرض المبيعات
    const sortedSales = this.app.data.sales.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedSales.forEach(sale => {
      const saleDiv = document.createElement('div');
      saleDiv.className = 'sale-item';
      saleDiv.innerHTML = `
        <div class="sale-header">
          <h4>فاتورة رقم: ${sale.invoiceNumber}</h4>
          <span class="sale-amount">${formatCurrency(sale.amount)}</span>
        </div>
        <div class="sale-details">
          <p><strong>العميل:</strong> ${sale.customerName}</p>
          <p><strong>نوع الخدمة:</strong> ${sale.serviceType}</p>
          <p><strong>التاريخ:</strong> ${formatDate(sale.date)}</p>
        </div>
        <div class="sale-actions">
          <button class="action-btn edit" onclick="editSale(${sale.id})">تعديل</button>
          <button class="action-btn print" onclick="printSaleInvoice(${sale.id})">طباعة</button>
          <button class="action-btn delete" onclick="deleteSale(${sale.id})">حذف</button>
        </div>
      `;
      container.appendChild(saleDiv);
    });
  }

  // تحميل بيانات المشتريات
  loadPurchasesData() {
    const container = document.getElementById('purchases-list');
    if (!container) return;

    container.innerHTML = '';

    const sortedPurchases = this.app.data.purchases.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedPurchases.forEach(purchase => {
      const purchaseDiv = document.createElement('div');
      purchaseDiv.className = 'purchase-item';
      purchaseDiv.innerHTML = `
        <div class="purchase-header">
          <h4>فاتورة رقم: ${purchase.invoiceNumber}</h4>
          <span class="purchase-amount">${formatCurrency(purchase.amount)}</span>
        </div>
        <div class="purchase-details">
          <p><strong>المورد:</strong> ${purchase.supplierName}</p>
          <p><strong>التاريخ:</strong> ${formatDate(purchase.date)}</p>
          <p><strong>الوصف:</strong> ${purchase.description}</p>
        </div>
        <div class="purchase-actions">
          <button class="action-btn edit" onclick="editPurchase(${purchase.id})">تعديل</button>
          <button class="action-btn print" onclick="printPurchaseInvoice(${purchase.id})">طباعة</button>
          <button class="action-btn delete" onclick="deletePurchase(${purchase.id})">حذف</button>
        </div>
      `;
      container.appendChild(purchaseDiv);
    });
  }

  // تحميل بيانات الديون
  loadDebtsData() {
    const container = document.getElementById('debts-list');
    if (!container) return;

    container.innerHTML = '';

    let totalDebts = 0;
    let overdueDebts = 0;

    this.app.data.debts.forEach(debt => {
      if (debt.status !== 'paid') {
        totalDebts += debt.amount;
        
        const isOverdue = new Date(debt.dueDate) < new Date();
        if (isOverdue) {
          overdueDebts += debt.amount;
        }
      }

      let statusClass = 'status-active';
      let statusText = 'نشط';

      if (debt.status === 'paid') {
        statusClass = 'status-paid';
        statusText = 'مدفوع';
      } else if (new Date(debt.dueDate) < new Date()) {
        statusClass = 'status-overdue';
        statusText = 'متأخر';
      }

      const debtDiv = document.createElement('div');
      debtDiv.className = 'debt-item';
      debtDiv.innerHTML = `
        <div class="debt-header">
          <h4>${debt.customerName}</h4>
          <span class="debt-amount">${formatCurrency(debt.amount)}</span>
        </div>
        <div class="debt-details">
          <p><strong>تاريخ الاستحقاق:</strong> ${formatDate(debt.dueDate)}</p>
          <p><strong>الحالة:</strong> <span class="${statusClass}">${statusText}</span></p>
          <p><strong>الوصف:</strong> ${debt.description}</p>
        </div>
        <div class="debt-actions">
          <button class="action-btn edit" onclick="editDebt(${debt.id})">تعديل</button>
          ${debt.status !== 'paid' ? `<button class="action-btn" onclick="markDebtAsPaid(${debt.id})">تسديد</button>` : ''}
          <button class="action-btn delete" onclick="deleteDebt(${debt.id})">حذف</button>
        </div>
      `;
      container.appendChild(debtDiv);
    });

    // تحديث الإحصائيات
    document.getElementById('total-debts').textContent = formatCurrency(totalDebts);
    document.getElementById('overdue-debts').textContent = formatCurrency(overdueDebts);
  }

  // تحميل بيانات جدول الإرسال
  loadDispatchData() {
    const container = document.getElementById('dispatch-list');
    if (!container) return;

    container.innerHTML = '';

    const sortedDispatch = this.app.data.dispatch.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedDispatch.forEach(dispatch => {
      const dispatchDiv = document.createElement('div');
      dispatchDiv.className = 'dispatch-item';
      dispatchDiv.innerHTML = `
        <div class="dispatch-header">
          <h4>إرسالية رقم: ${dispatch.dispatchNumber}</h4>
          <span class="dispatch-date">${formatDate(dispatch.date)}</span>
        </div>
        <div class="dispatch-details">
          <p><strong>السائق:</strong> ${dispatch.driverName}</p>
          <p><strong>الوجهة:</strong> ${dispatch.destination}</p>
          <p><strong>الحالة:</strong> ${dispatch.status}</p>
        </div>
        <div class="dispatch-actions">
          <button class="action-btn edit" onclick="editDispatch(${dispatch.id})">تعديل</button>
          <button class="action-btn print" onclick="printDispatchReport(${dispatch.id})">طباعة</button>
          <button class="action-btn delete" onclick="deleteDispatch(${dispatch.id})">حذف</button>
        </div>
      `;
      container.appendChild(dispatchDiv);
    });
  }

  // تحميل بيانات الإعدادات
  loadSettingsData() {
    const settings = this.app.data.settings;
    
    document.getElementById('company-name').value = settings.companyName || '';
    document.getElementById('company-address').value = settings.companyAddress || '';
    document.getElementById('company-phone').value = settings.companyPhone || '';
    document.getElementById('language-select').value = settings.language || 'ar';
    document.getElementById('currency-select').value = settings.currency || 'SAR';
  }
}

// إضافة DataManager إلى النافذة العامة
window.DataManager = DataManager;
