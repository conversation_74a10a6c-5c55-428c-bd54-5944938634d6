<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>نظام إدارة مؤسسة وقود المستقبل</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <h1>نظام إدارة مؤسسة وقود المستقبل</h1>
  </header>

  <nav>
    <button>🏠 الرئيسية</button>
    <button>🆔 بطاقات الغاز</button>
    <button>📅 المواعيد</button>
    <button>👥 الزبائن</button>
    <button>🚚 الموردين</button>
    <button>📦 المخزون</button>
    <button>🛒 المبيعات</button>
    <button>🛍️ المشتريات</button>
    <button>💰 الديون</button>
    <button>📊 جدول الإرسال</button>
    <button>⚙️ الإعدادات</button>
  </nav>

  <main id="main-content">
    <div id="dashboard" class="page active">
      <h2>لوحة التحكم</h2>
      <div class="dashboard-cards">
        <div class="card">
          <h3>🆔 بطاقات الغاز</h3>
          <p>إدارة بطاقات الغاز والعملاء</p>
          <div class="card-stats">
            <span id="gas-cards-count">0</span> بطاقة نشطة
          </div>
        </div>
        <div class="card">
          <h3>📅 المواعيد</h3>
          <p>جدولة وإدارة المواعيد</p>
          <div class="card-stats">
            <span id="appointments-count">0</span> موعد اليوم
          </div>
        </div>
        <div class="card">
          <h3>👥 الزبائن</h3>
          <p>قاعدة بيانات العملاء</p>
          <div class="card-stats">
            <span id="customers-count">0</span> عميل
          </div>
        </div>
        <div class="card">
          <h3>🚚 الموردين</h3>
          <p>إدارة الموردين والمشتريات</p>
          <div class="card-stats">
            <span id="suppliers-count">0</span> مورد
          </div>
        </div>
      </div>
    </div>

    <div id="gas-cards" class="page">
      <h2>🆔 بطاقات الغاز</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddGasCardForm()">إضافة بطاقة جديدة</button>
        <button class="btn-secondary" onclick="exportGasCards()">تصدير البيانات</button>
      </div>
      <div class="table-container">
        <table id="gas-cards-table">
          <thead>
            <tr>
              <th>رقم البطاقة</th>
              <th>اسم العميل</th>
              <th>نوع الغاز</th>
              <th>الرصيد</th>
              <th>تاريخ الإنشاء</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم ملء البيانات بواسطة JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="appointments" class="page">
      <h2>📅 المواعيد</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddAppointmentForm()">إضافة موعد جديد</button>
        <button class="btn-secondary" onclick="showCalendarView()">عرض التقويم</button>
      </div>
      <div class="appointments-container">
        <div class="appointments-list" id="appointments-list">
          <!-- سيتم ملء المواعيد بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="customers" class="page">
      <h2>👥 الزبائن</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddCustomerForm()">إضافة عميل جديد</button>
        <button class="btn-secondary" onclick="exportCustomers()">تصدير قائمة العملاء</button>
      </div>
      <div class="table-container">
        <table id="customers-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>رقم الهاتف</th>
              <th>العنوان</th>
              <th>نوع العميل</th>
              <th>تاريخ التسجيل</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم ملء البيانات بواسطة JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="suppliers" class="page">
      <h2>🚚 الموردين</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddSupplierForm()">إضافة مورد جديد</button>
        <button class="btn-secondary" onclick="exportSuppliers()">تصدير قائمة الموردين</button>
      </div>
      <div class="table-container">
        <table id="suppliers-table">
          <thead>
            <tr>
              <th>اسم المورد</th>
              <th>رقم الهاتف</th>
              <th>العنوان</th>
              <th>نوع المنتج</th>
              <th>تاريخ التسجيل</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم ملء البيانات بواسطة JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="inventory" class="page">
      <h2>📦 المخزون</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddInventoryForm()">إضافة منتج</button>
        <button class="btn-secondary" onclick="generateInventoryReport()">تقرير المخزون</button>
      </div>
      <div class="inventory-grid">
        <div class="inventory-summary">
          <h3>ملخص المخزون</h3>
          <div class="summary-cards">
            <div class="summary-card">
              <h4>إجمالي المنتجات</h4>
              <span id="total-products">0</span>
            </div>
            <div class="summary-card">
              <h4>منتجات منخفضة المخزون</h4>
              <span id="low-stock-products">0</span>
            </div>
          </div>
        </div>
        <div class="inventory-list" id="inventory-list">
          <!-- سيتم ملء المخزون بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="sales" class="page">
      <h2>🛒 المبيعات</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showNewSaleForm()">عملية بيع جديدة</button>
        <button class="btn-secondary" onclick="generateSalesReport()">تقرير المبيعات</button>
      </div>
      <div class="sales-container">
        <div class="sales-summary">
          <h3>ملخص المبيعات اليوم</h3>
          <div class="summary-stats">
            <div class="stat">
              <label>إجمالي المبيعات:</label>
              <span id="daily-sales-total">0 ريال</span>
            </div>
            <div class="stat">
              <label>عدد العمليات:</label>
              <span id="daily-sales-count">0</span>
            </div>
          </div>
        </div>
        <div class="sales-list" id="sales-list">
          <!-- سيتم ملء المبيعات بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="purchases" class="page">
      <h2>🛍️ المشتريات</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showNewPurchaseForm()">عملية شراء جديدة</button>
        <button class="btn-secondary" onclick="generatePurchasesReport()">تقرير المشتريات</button>
      </div>
      <div class="purchases-container">
        <div class="purchases-list" id="purchases-list">
          <!-- سيتم ملء المشتريات بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="debts" class="page">
      <h2>💰 الديون</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddDebtForm()">إضافة دين جديد</button>
        <button class="btn-secondary" onclick="generateDebtsReport()">تقرير الديون</button>
      </div>
      <div class="debts-container">
        <div class="debts-summary">
          <h3>ملخص الديون</h3>
          <div class="summary-stats">
            <div class="stat">
              <label>إجمالي الديون:</label>
              <span id="total-debts">0 ريال</span>
            </div>
            <div class="stat">
              <label>ديون مستحقة:</label>
              <span id="overdue-debts">0 ريال</span>
            </div>
          </div>
        </div>
        <div class="debts-list" id="debts-list">
          <!-- سيتم ملء الديون بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="dispatch" class="page">
      <h2>📊 جدول الإرسال</h2>
      <div class="page-actions">
        <button class="btn-primary" onclick="showAddDispatchForm()">إضافة عملية إرسال</button>
        <button class="btn-secondary" onclick="generateDispatchReport()">تقرير الإرسال</button>
      </div>
      <div class="dispatch-container">
        <div class="dispatch-list" id="dispatch-list">
          <!-- سيتم ملء جدول الإرسال بواسطة JavaScript -->
        </div>
      </div>
    </div>

    <div id="settings" class="page">
      <h2>⚙️ الإعدادات</h2>
      <div class="settings-container">
        <div class="settings-section">
          <h3>إعدادات عامة</h3>
          <div class="setting-item">
            <label>اسم المؤسسة:</label>
            <input type="text" id="company-name" value="مؤسسة وقود المستقبل">
          </div>
          <div class="setting-item">
            <label>عنوان المؤسسة:</label>
            <input type="text" id="company-address" value="">
          </div>
          <div class="setting-item">
            <label>رقم الهاتف:</label>
            <input type="text" id="company-phone" value="">
          </div>
        </div>
        <div class="settings-section">
          <h3>إعدادات النظام</h3>
          <div class="setting-item">
            <label>اللغة:</label>
            <select id="language-select">
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
          <div class="setting-item">
            <label>العملة:</label>
            <select id="currency-select">
              <option value="SAR">ريال سعودي</option>
              <option value="USD">دولار أمريكي</option>
            </select>
          </div>
        </div>
        <div class="settings-actions">
          <button class="btn-primary" onclick="saveSettings()">حفظ الإعدادات</button>
          <button class="btn-secondary" onclick="resetSettings()">إعادة تعيين</button>
        </div>
      </div>
    </div>
  </main>

  <!-- تحميل ملفات JavaScript -->
  <script src="scripts/app.js"></script>
  <script src="scripts/data-manager.js"></script>
  <script src="scripts/forms.js"></script>
  <script src="scripts/reports.js"></script>
  <script src="scripts/calendar.js"></script>
  <script>
    // تهيئة مدراء البيانات والنماذج
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        window.dataManager = new DataManager(window.app);
        window.formsManager = new FormsManager(window.app);
        window.reportsManager = new ReportsManager(window.app);
        window.calendarManager = new CalendarManager(window.app);

        // ربط مدراء البيانات بالتطبيق
        window.app.dataManager = window.dataManager;
        window.app.formsManager = window.formsManager;
        window.app.reportsManager = window.reportsManager;
        window.app.calendarManager = window.calendarManager;
      }, 100);
    });

    // دوال تغيير اللغة
    function changeLanguage(lang) {
      window.app.loadLanguage(lang);
    }

    // دوال حفظ الإعدادات
    function saveSettings() {
      const settings = {
        companyName: document.getElementById('company-name').value,
        companyAddress: document.getElementById('company-address').value,
        companyPhone: document.getElementById('company-phone').value,
        language: document.getElementById('language-select').value,
        currency: document.getElementById('currency-select').value
      };

      window.app.data.settings = { ...window.app.data.settings, ...settings };
      window.app.saveData();
      window.app.showAlert('تم حفظ الإعدادات بنجاح', 'success');
    }

    function resetSettings() {
      if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات؟')) {
        window.app.data.settings = {
          companyName: 'مؤسسة وقود المستقبل',
          language: 'ar',
          currency: 'SAR'
        };
        window.app.saveData();
        window.app.dataManager.loadSettingsData();
        window.app.showAlert('تم إعادة تعيين الإعدادات', 'info');
      }
    }
  </script>
</body>
</html>