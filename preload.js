const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات التطبيق
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: () => ipcRenderer.invoke('get-app-path'),

  // إدارة الملفات
  readFile: (filePath) => {
    try {
      return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
      console.error('Error reading file:', error);
      return null;
    }
  },

  writeFile: (filePath, data) => {
    try {
      fs.writeFileSync(filePath, data, 'utf8');
      return true;
    } catch (error) {
      console.error('Error writing file:', error);
      return false;
    }
  },

  fileExists: (filePath) => {
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      return false;
    }
  },

  // إدارة المجلدات
  createDirectory: (dirPath) => {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      return true;
    } catch (error) {
      console.error('Error creating directory:', error);
      return false;
    }
  },

  // مسارات التطبيق
  getDataPath: () => path.join(__dirname, 'data'),
  getLangPath: () => path.join(__dirname, 'lang'),
  getAssetsPath: () => path.join(__dirname, 'assets'),

  // تحميل ملفات اللغة
  loadLanguage: (lang) => {
    try {
      const langFile = path.join(__dirname, 'lang', `${lang}.json`);
      if (fs.existsSync(langFile)) {
        const content = fs.readFileSync(langFile, 'utf8');
        return JSON.parse(content);
      }
      return null;
    } catch (error) {
      console.error('Error loading language file:', error);
      return null;
    }
  },

  // حفظ البيانات
  saveData: (filename, data) => {
    try {
      const dataPath = path.join(__dirname, 'data', filename);
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error('Error saving data:', error);
      return false;
    }
  },

  // تحميل البيانات
  loadData: (filename) => {
    try {
      const dataPath = path.join(__dirname, 'data', filename);
      if (fs.existsSync(dataPath)) {
        const content = fs.readFileSync(dataPath, 'utf8');
        return JSON.parse(content);
      }
      return null;
    } catch (error) {
      console.error('Error loading data:', error);
      return null;
    }
  },

  // طباعة
  print: () => {
    window.print();
  },

  // تاريخ ووقت
  getCurrentDate: () => {
    return new Date().toISOString();
  },

  formatDate: (date, locale = 'ar-SA') => {
    return new Date(date).toLocaleDateString(locale);
  },

  formatDateTime: (date, locale = 'ar-SA') => {
    return new Date(date).toLocaleString(locale);
  }
});

// إعداد معالجات الأحداث العامة
window.addEventListener('DOMContentLoaded', () => {
  console.log('Fuel Management App - Preload script loaded');
});